<template>
  <div class="lifestyle-container">
    <!-- 去过&想去 -->
    <div class="travel-section">
      <div class="section-header">
        <span class="section-icon">✈️</span>
        <span class="section-title">去过&想去</span>
        <div class="section-actions">
          <button class="section-mic-btn" title="语音" @click="handleTravelMicClick">
            <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
          </button>
          <button class="section-edit-btn" title="编辑" @click="handleTravelSectionArrowClick">
            <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <!-- 编辑模式 -->
        <div v-if="showTravelEdit" class="edit-list-container">
          <div v-for="(value, index) in travelEditValues" :key="index" class="edit-item-container">
            <input
              v-model="travelEditValues[index]"
              type="text"
              class="attribute-value"
              :placeholder="`旅行地点 ${index + 1}`"
              @keydown.enter="handleTravelEditComplete"
              @keydown.esc="handleTravelEditCancel"
            />
          </div>
          <!-- 空白输入框用于添加新项 -->
          <div class="edit-item-container">
            <input
              v-model="newTravelItem"
              type="text"
              class="attribute-value"
              placeholder="添加新的旅行地点"
              @keydown.enter="handleTravelEditComplete"
              @keydown.esc="handleTravelEditCancel"
            />
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleTravelEditComplete">保存</button>
            <button class="cancel-btn" @click="handleTravelEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="travel-content">
          <div v-if="getTravelHistory()" class="travel-info">
            <div v-for="(location, index) in getTravelHistoryList()" :key="index" class="travel-item">
              <!-- 单个item编辑模式 -->
              <div v-if="editingTravelIndex === index" class="item-edit-mode">
                <input
                  v-model="tempTravelEditValue"
                  type="text"
                  class="item-edit-input"
                  :placeholder="`旅行地点 ${index + 1}`"
                  @keydown.enter="handleTravelItemEditComplete(index)"
                  @keydown.esc="handleTravelItemEditCancel"
                />
                <div class="item-edit-actions">
                  <button class="item-save-btn" @click="handleTravelItemEditComplete(index)">保存</button>
                  <button class="item-cancel-btn" @click="handleTravelItemEditCancel">取消</button>
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="item-display-mode" @click="handleTravelItemEdit(index, location)">
                {{ location }}
                <button class="delete-item-btn" title="删除" @click.stop="handleDeleteTravelItem(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="empty-travel">记录每一次旅行足迹，分享美好旅途回忆。</div>
        </div>
      </div>
    </div>

    <!-- 饮食偏好 -->
    <div class="food-section">
      <div class="section-header">
        <span class="section-icon">🍽️</span>
        <span class="section-title">饮食偏好</span>
        <div class="section-actions">
          <button class="section-mic-btn" title="语音" @click="handleFoodMicClick">
            <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
          </button>
          <button class="section-edit-btn" title="编辑" @click="handleFoodSectionArrowClick">
            <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <!-- 编辑模式 -->
        <div v-if="showFoodEdit" class="edit-list-container">
          <div v-for="(value, index) in foodEditValues" :key="index" class="edit-item-container">
            <input
              v-model="foodEditValues[index]"
              type="text"
              class="attribute-value"
              :placeholder="`饮食偏好 ${index + 1}`"
              @keydown.enter="handleFoodEditComplete"
              @keydown.esc="handleFoodEditCancel"
            />
          </div>
          <!-- 空白输入框用于添加新项 -->
          <div class="edit-item-container">
            <input
              v-model="newFoodItem"
              type="text"
              class="attribute-value"
              placeholder="添加新的饮食偏好"
              @keydown.enter="handleFoodEditComplete"
              @keydown.esc="handleFoodEditCancel"
            />
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleFoodEditComplete">保存</button>
            <button class="cancel-btn" @click="handleFoodEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="food-content">
          <div v-if="getFoodPreference()" class="food-info">
            <div v-for="(preference, index) in getFoodPreferenceList()" :key="index" class="food-item">
              <!-- 单个item编辑模式 -->
              <div v-if="editingFoodIndex === index" class="item-edit-mode">
                <input
                  v-model="tempFoodEditValue"
                  type="text"
                  class="item-edit-input"
                  :placeholder="`饮食偏好 ${index + 1}`"
                  @keydown.enter="handleFoodItemEditComplete(index)"
                  @keydown.esc="handleFoodItemEditCancel"
                />
                <div class="item-edit-actions">
                  <button class="item-save-btn" @click="handleFoodItemEditComplete(index)">保存</button>
                  <button class="item-cancel-btn" @click="handleFoodItemEditCancel">取消</button>
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="item-display-mode" @click="handleFoodItemEdit(index, preference)">
                {{ preference }}
                <button class="delete-item-btn" title="删除" @click.stop="handleDeleteFoodItem(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="empty-food">了解TA的口味偏好，为下次聚餐做好准备。</div>
        </div>
      </div>
    </div>

    <!-- 我的期望 -->
    <div class="expectation-section">
      <div class="section-header">
        <span class="section-icon">🌟</span>
        <span class="section-title">我的期望</span>
        <div class="section-actions">
          <button class="section-mic-btn" title="语音" @click="handleExpectationMicClick">
            <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
          </button>
          <button class="section-edit-btn" title="编辑" @click="handleExpectationSectionArrowClick">
            <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <!-- 编辑模式 -->
        <div v-if="showExpectationEdit" class="edit-list-container">
          <div v-for="(value, index) in expectationEditValues" :key="index" class="edit-item-container">
            <input
              v-model="expectationEditValues[index]"
              type="text"
              class="attribute-value"
              :placeholder="`期望 ${index + 1}`"
              @keydown.enter="handleExpectationEditComplete"
              @keydown.esc="handleExpectationEditCancel"
            />
          </div>
          <!-- 空白输入框用于添加新项 -->
          <div class="edit-item-container">
            <input
              v-model="newExpectationItem"
              type="text"
              class="attribute-value"
              placeholder="添加新的期望"
              @keydown.enter="handleExpectationEditComplete"
              @keydown.esc="handleExpectationEditCancel"
            />
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleExpectationEditComplete">保存</button>
            <button class="cancel-btn" @click="handleExpectationEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="expectation-content">
          <div v-if="getExpectationList().length > 0" class="expectation-info">
            <div v-for="(expectation, index) in getExpectationList()" :key="index" class="expectation-item">
              <!-- 单个item编辑模式 -->
              <div v-if="editingExpectationIndex === index" class="item-edit-mode">
                <input
                  v-model="tempExpectationEditValue"
                  type="text"
                  class="item-edit-input"
                  :placeholder="`期望 ${index + 1}`"
                  @keydown.enter="handleExpectationItemEditComplete(index)"
                  @keydown.esc="handleExpectationItemEditCancel"
                />
                <div class="item-edit-actions">
                  <button class="item-save-btn" @click="handleExpectationItemEditComplete(index)">保存</button>
                  <button class="item-cancel-btn" @click="handleExpectationItemEditCancel">取消</button>
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="item-display-mode" @click="handleExpectationItemEdit(index, expectation)">
                {{ expectation }}
                <button class="delete-item-btn" title="删除" @click.stop="handleDeleteExpectationItem(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="empty-expectation">记录你的期望和心愿。</div>
        </div>
      </div>
    </div>

    <!-- 其他属性信息 -->
    <div class="other-attributes-section">
      <div class="section-header">
        <span class="section-icon">📝</span>
        <span class="section-title">其他信息</span>
        <div class="section-actions">
          <button class="section-add-btn" title="添加" @click="handleAddOtherAttribute">
            <span class="add-icon">+</span>
          </button>
        </div>
      </div>
      <div class="section-content">
        <div v-if="getOtherAttributes().length > 0" class="other-attributes-content">
          <div v-for="(attr, index) in getOtherAttributes()" :key="index" class="attribute-item">
            <!-- 单个attribute编辑模式 -->
            <div v-if="editingOtherAttributeIndex === index" class="item-edit-mode">
              <div class="attribute-edit-container">
                <input
                  v-model="tempOtherAttributeEditKey"
                  type="text"
                  class="item-edit-input attribute-key-input"
                  placeholder="属性名称"
                  @keydown.enter="handleOtherAttributeItemEditComplete(index)"
                  @keydown.esc="handleOtherAttributeItemEditCancel"
                />
                <span class="attribute-separator">:</span>
                <input
                  v-model="tempOtherAttributeEditValue"
                  type="text"
                  class="item-edit-input attribute-value-input"
                  placeholder="属性值"
                  @keydown.enter="handleOtherAttributeItemEditComplete(index)"
                  @keydown.esc="handleOtherAttributeItemEditCancel"
                />
              </div>
              <div class="item-edit-actions">
                <button class="item-save-btn" @click="handleOtherAttributeItemEditComplete(index)">保存</button>
                <button class="item-cancel-btn" @click="handleOtherAttributeItemEditCancel">取消</button>
              </div>
            </div>
            <!-- 显示模式 -->
            <div v-else class="item-display-mode" @click="handleOtherAttributeItemEdit(index, attr.key, attr.value)">
              <div class="attribute-label">{{ attr.key }}:</div>
              <div class="attribute-value">{{ attr.value }}</div>
              <button class="delete-item-btn" title="删除" @click.stop="handleDeleteOtherAttribute(index)">
                <span class="delete-icon">×</span>
              </button>
            </div>
          </div>
        </div>
        <div v-else class="empty-other-attributes">暂无其他属性信息</div>

        <!-- 新增属性编辑模式 -->
        <div v-if="showAddOtherAttribute" class="add-attribute-mode">
          <div class="attribute-edit-container">
            <input
              v-model="newOtherAttributeKey"
              type="text"
              class="item-edit-input attribute-key-input"
              placeholder="属性名称"
              @keydown.enter="handleAddOtherAttributeComplete"
              @keydown.esc="handleAddOtherAttributeCancel"
            />
            <span class="attribute-separator">:</span>
            <input
              v-model="newOtherAttributeValue"
              type="text"
              class="item-edit-input attribute-value-input"
              placeholder="属性值"
              @keydown.enter="handleAddOtherAttributeComplete"
              @keydown.esc="handleAddOtherAttributeCancel"
            />
          </div>
          <div class="item-edit-actions">
            <button class="item-save-btn" @click="handleAddOtherAttributeComplete">保存</button>
            <button class="item-cancel-btn" @click="handleAddOtherAttributeCancel">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    :content="`确定要删除这个${deleteItemInfo.type}吗？`"
    :hint="deleteItemInfo.content"
    @confirm="confirmDeleteItem"
    @cancel="closeDeleteDialog"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { type IPersonDetail } from '@/apis/memory';
import { updatePerson } from '@/apis/relation';
import { showSuccessToast, showFailToast } from 'vant';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  travelSectionArrowClick: [];
  travelEditComplete: [values: string[], newItem: string];
  travelEditCancel: [];
  travelItemEdit: [index: number, value: string];
  travelItemEditComplete: [index: number, value: string];
  travelItemEditCancel: [];
  deleteTravelItem: [index: number];
  foodSectionArrowClick: [];
  foodEditComplete: [values: string[], newItem: string];
  foodEditCancel: [];
  foodItemEdit: [index: number, value: string];
  foodItemEditComplete: [index: number, value: string];
  foodItemEditCancel: [];
  deleteFoodItem: [index: number];
  expectationSectionArrowClick: [];
  expectationEditComplete: [values: string[], newItem: string];
  expectationEditCancel: [];
  expectationItemEdit: [index: number, value: string];
  expectationItemEditComplete: [index: number, value: string];
  expectationItemEditCancel: [];
  deleteExpectationItem: [index: number];
  addOtherAttribute: [];
  addOtherAttributeComplete: [key: string, value: string];
  addOtherAttributeCancel: [];
  otherAttributeItemEdit: [index: number, key: string, value: string];
  otherAttributeItemEditComplete: [index: number, key: string, value: string];
  otherAttributeItemEditCancel: [];
  deleteOtherAttribute: [index: number];
  // 修改为传递更新后的属性数据
  attributesUpdated: [newAttributes: Record<string, string>];
  // 添加语音聊天相关事件
  showVoiceChat: [sectionInfo: { title: string; icon: string; content: string }];
}>();

// 响应式数据
const showTravelEdit = ref(false);
const travelEditValues = ref<string[]>([]);
const newTravelItem = ref('');
const editingTravelIndex = ref<number | null>(null);
const tempTravelEditValue = ref('');

// 删除确认弹窗相关
const showDeleteDialog = ref(false);
const deleteItemInfo = ref<{
  type: string;
  content: string;
  category: 'travel' | 'food' | 'expectation' | 'other_attribute';
  index: number;
}>({
  type: '',
  content: '',
  category: 'travel',
  index: -1,
});

const showFoodEdit = ref(false);
const foodEditValues = ref<string[]>([]);
const newFoodItem = ref('');
const editingFoodIndex = ref<number | null>(null);
const tempFoodEditValue = ref('');

const showExpectationEdit = ref(false);
const expectationEditValues = ref<string[]>([]);
const newExpectationItem = ref('');
const editingExpectationIndex = ref<number | null>(null);
const tempExpectationEditValue = ref('');

const showAddOtherAttribute = ref(false);
const newOtherAttributeKey = ref('');
const newOtherAttributeValue = ref('');
const editingOtherAttributeIndex = ref<number | null>(null);
const tempOtherAttributeEditKey = ref('');
const tempOtherAttributeEditValue = ref('');

// 计算属性：获取处理后的key_attributes对象
const processedKeyAttributes = computed(() => {
  if (!props.personDetail || !props.personDetail.key_attributes) {
    return {};
  }

  let attributes = props.personDetail.key_attributes;

  // 如果是字符串，尝试解析为对象
  if (typeof attributes === 'string') {
    try {
      attributes = JSON.parse(attributes);
    } catch (error) {
      console.warn('⚠️ [LifestyleSection] key_attributes字符串解析失败:', error);
      return {};
    }
  }

  // 确保是对象类型
  if (typeof attributes !== 'object' || attributes === null) {
    console.warn('⚠️ [LifestyleSection] key_attributes不是有效对象:', attributes);
    return {};
  }

  // 过滤掉空值的属性
  const filteredAttributes: Record<string, string> = {};
  Object.entries(attributes).forEach(([key, value]) => {
    if (value && String(value).trim()) {
      filteredAttributes[key] = String(value);
    }
  });

  return filteredAttributes;
});

// 获取旅游历史属性值
const getTravelHistory = (): string => {
  return processedKeyAttributes.value['旅游历史'] || '';
};

// 获取旅游历史列表（分割后的数组）
const getTravelHistoryList = (): string[] => {
  const travelHistory = getTravelHistory();
  if (!travelHistory) return [];
  return travelHistory
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取餐饮偏好属性值
const getFoodPreference = (): string => {
  return processedKeyAttributes.value['餐饮偏好'] || '';
};

// 获取餐饮偏好列表（分割后的数组）
const getFoodPreferenceList = (): string[] => {
  const foodPreference = getFoodPreference();
  if (!foodPreference) return [];
  return foodPreference
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 去过&想去相关方法
const handleTravelMicClick = () => {
  const attributes = processedKeyAttributes.value;
  const sectionInfo = {
    title: '旅行记录',
    icon: '✈️',
    content: attributes['旅游历史'] || '暂无旅行记录',
  };
  emit('showVoiceChat', sectionInfo);
};

const handleTravelSectionArrowClick = () => {
  showTravelEdit.value = true;
  const travelList = getTravelHistoryList();
  travelEditValues.value = [...travelList];
  newTravelItem.value = '';
  emit('travelSectionArrowClick');
};

const handleTravelEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    // 合并现有值和新值
    const allValues = [...travelEditValues.value];
    if (newTravelItem.value.trim()) {
      allValues.push(newTravelItem.value.trim());
    }

    // 过滤空值
    const filteredValues = allValues.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (filteredValues.length > 0) {
      attributes['旅游历史'] = filteredValues.join(', ');
    } else {
      delete attributes['旅游历史'];
    }

    await updatePersonAttributes(attributes);
    showTravelEdit.value = false;
    showSuccessToast('旅行记录保存成功');
  } catch (error) {
    console.error('保存旅行记录失败:', error);
    showFailToast('保存旅行记录失败');
  }
};

const handleTravelEditCancel = () => {
  showTravelEdit.value = false;
  travelEditValues.value = [];
  newTravelItem.value = '';
  emit('travelEditCancel');
};

const handleTravelItemEdit = (index: number, value: string) => {
  editingTravelIndex.value = index;
  tempTravelEditValue.value = value;
  emit('travelItemEdit', index, value);
};

const handleTravelItemEditComplete = (index: number) => {
  emit('travelItemEditComplete', index, tempTravelEditValue.value);
  editingTravelIndex.value = null;
  tempTravelEditValue.value = '';
};

const handleTravelItemEditCancel = () => {
  editingTravelIndex.value = null;
  tempTravelEditValue.value = '';
  emit('travelItemEditCancel');
};

const handleDeleteTravelItem = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
  const travelHistory = attributes['旅游历史'];

  if (travelHistory) {
    const travelList = travelHistory
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item);

    if (index >= 0 && index < travelList.length) {
      const itemToDelete = travelList[index];

      // 设置删除确认弹窗信息
      deleteItemInfo.value = {
        type: '旅行记录',
        content: itemToDelete,
        category: 'travel',
        index,
      };
      showDeleteDialog.value = true;
    }
  }
};

// 饮食偏好相关方法
const handleFoodMicClick = () => {
  const attributes = processedKeyAttributes.value;
  const sectionInfo = {
    title: '饮食偏好',
    icon: '🍽️',
    content: attributes['餐饮偏好'] || '暂无饮食偏好信息',
  };
  emit('showVoiceChat', sectionInfo);
};

const handleFoodSectionArrowClick = () => {
  showFoodEdit.value = true;
  const foodList = getFoodPreferenceList();
  foodEditValues.value = [...foodList];
  newFoodItem.value = '';
  emit('foodSectionArrowClick');
};

const handleFoodEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    // 合并现有值和新值
    const allValues = [...foodEditValues.value];
    if (newFoodItem.value.trim()) {
      allValues.push(newFoodItem.value.trim());
    }

    // 过滤空值
    const filteredValues = allValues.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (filteredValues.length > 0) {
      attributes['餐饮偏好'] = filteredValues.join(', ');
    } else {
      delete attributes['餐饮偏好'];
    }

    await updatePersonAttributes(attributes);
    showFoodEdit.value = false;
    showSuccessToast('饮食偏好保存成功');
  } catch (error) {
    console.error('保存饮食偏好失败:', error);
    showFailToast('保存饮食偏好失败');
  }
};

const handleFoodEditCancel = () => {
  showFoodEdit.value = false;
  foodEditValues.value = [];
  newFoodItem.value = '';
  emit('foodEditCancel');
};

const handleFoodItemEdit = (index: number, value: string) => {
  editingFoodIndex.value = index;
  tempFoodEditValue.value = value;
  emit('foodItemEdit', index, value);
};

const handleFoodItemEditComplete = (index: number) => {
  emit('foodItemEditComplete', index, tempFoodEditValue.value);
  editingFoodIndex.value = null;
  tempFoodEditValue.value = '';
};

const handleFoodItemEditCancel = () => {
  editingFoodIndex.value = null;
  tempFoodEditValue.value = '';
  emit('foodItemEditCancel');
};

const handleDeleteFoodItem = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
  const foodPreferences = attributes['餐饮偏好'];

  if (foodPreferences) {
    const foodList = foodPreferences
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item);

    if (index >= 0 && index < foodList.length) {
      const itemToDelete = foodList[index];

      // 设置删除确认弹窗信息
      deleteItemInfo.value = {
        type: '饮食偏好',
        content: itemToDelete,
        category: 'food',
        index,
      };
      showDeleteDialog.value = true;
    }
  }
};

// 获取期望列表（从多个可能的属性中获取）
const getExpectationList = (): string[] => {
  const expectations: string[] = [];

  // 检查所有可能的期望相关属性
  const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];

  expectationKeys.forEach((key) => {
    const value = processedKeyAttributes.value[key];
    if (value) {
      const items = value
        .split('|')
        .map((item) => item.trim())
        .filter((item) => item.length > 0);
      expectations.push(...items);
    }
  });

  return expectations;
};

// 期望相关方法
const handleExpectationMicClick = () => {
  const attributes = processedKeyAttributes.value;
  const sectionInfo = {
    title: '我的期望',
    icon: '🌟',
    content: attributes['期望'] || '暂无期望信息',
  };
  emit('showVoiceChat', sectionInfo);
};

const handleExpectationSectionArrowClick = () => {
  showExpectationEdit.value = true;
  const expectationList = getExpectationList();
  expectationEditValues.value = [...expectationList];
  newExpectationItem.value = '';
  emit('expectationSectionArrowClick');
};

const handleExpectationEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    // 合并现有值和新值
    const allValues = [...expectationEditValues.value];
    if (newExpectationItem.value.trim()) {
      allValues.push(newExpectationItem.value.trim());
    }

    // 过滤空值
    const filteredValues = allValues.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

    // 查找期望相关的属性键
    const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
    const foundKey = expectationKeys.find((key) => attributes[key]);
    const targetKey = foundKey || '期望'; // 默认使用'期望'

    if (filteredValues.length > 0) {
      attributes[targetKey] = filteredValues.join(', ');
    } else if (foundKey) {
      // 如果有现有的期望键，删除它
      delete attributes[foundKey];
    }

    await updatePersonAttributes(attributes);
    showExpectationEdit.value = false;
    showSuccessToast('期望保存成功');
  } catch (error) {
    console.error('保存期望失败:', error);
    showFailToast('保存期望失败');
  }
};

const handleExpectationEditCancel = () => {
  showExpectationEdit.value = false;
  expectationEditValues.value = [];
  newExpectationItem.value = '';
  emit('expectationEditCancel');
};

const handleExpectationItemEdit = (index: number, value: string) => {
  editingExpectationIndex.value = index;
  tempExpectationEditValue.value = value;
  emit('expectationItemEdit', index, value);
};

const handleExpectationItemEditComplete = (index: number) => {
  emit('expectationItemEditComplete', index, tempExpectationEditValue.value);
  editingExpectationIndex.value = null;
  tempExpectationEditValue.value = '';
};

const handleExpectationItemEditCancel = () => {
  editingExpectationIndex.value = null;
  tempExpectationEditValue.value = '';
  emit('expectationItemEditCancel');
};

const handleDeleteExpectationItem = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

  // 查找期望相关的属性
  const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
  const foundKey = expectationKeys.find((key) => attributes[key]);
  const expectationValue = foundKey ? attributes[foundKey] : '';

  if (expectationValue) {
    const expectationList = expectationValue
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item);

    if (index >= 0 && index < expectationList.length) {
      const itemToDelete = expectationList[index];

      // 设置删除确认弹窗信息
      deleteItemInfo.value = {
        type: '期望',
        content: itemToDelete,
        category: 'expectation',
        index,
      };
      showDeleteDialog.value = true;
    }
  }
};

// 获取其他属性列表（排除已知的特殊属性）
const getOtherAttributes = (): Array<{ key: string; value: string }> => {
  const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

  const otherAttributes: Array<{ key: string; value: string }> = [];

  Object.entries(processedKeyAttributes.value).forEach(([key, value]) => {
    if (!knownKeys.includes(key) && value && value.trim()) {
      otherAttributes.push({ key, value });
    }
  });

  return otherAttributes;
};

// 其他属性相关方法
const handleAddOtherAttribute = () => {
  showAddOtherAttribute.value = true;
  newOtherAttributeKey.value = '';
  newOtherAttributeValue.value = '';
  emit('addOtherAttribute');
};

const handleAddOtherAttributeComplete = () => {
  emit('addOtherAttributeComplete', newOtherAttributeKey.value, newOtherAttributeValue.value);
  showAddOtherAttribute.value = false;
  newOtherAttributeKey.value = '';
  newOtherAttributeValue.value = '';
};

const handleAddOtherAttributeCancel = () => {
  showAddOtherAttribute.value = false;
  newOtherAttributeKey.value = '';
  newOtherAttributeValue.value = '';
  emit('addOtherAttributeCancel');
};

const handleOtherAttributeItemEdit = (index: number, key: string, value: string) => {
  editingOtherAttributeIndex.value = index;
  tempOtherAttributeEditKey.value = key;
  tempOtherAttributeEditValue.value = value;
  emit('otherAttributeItemEdit', index, key, value);
};

const handleOtherAttributeItemEditComplete = (index: number) => {
  emit('otherAttributeItemEditComplete', index, tempOtherAttributeEditKey.value, tempOtherAttributeEditValue.value);
  editingOtherAttributeIndex.value = null;
  tempOtherAttributeEditKey.value = '';
  tempOtherAttributeEditValue.value = '';
};

const handleOtherAttributeItemEditCancel = () => {
  editingOtherAttributeIndex.value = null;
  tempOtherAttributeEditKey.value = '';
  tempOtherAttributeEditValue.value = '';
  emit('otherAttributeItemEditCancel');
};

const handleDeleteOtherAttribute = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
  const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

  // 获取其他属性列表
  const otherAttributes: Array<{ key: string; value: string }> = [];
  Object.entries(attributes).forEach(([key, value]) => {
    if (!knownKeys.includes(key) && value && value.trim()) {
      otherAttributes.push({ key, value });
    }
  });

  if (index >= 0 && index < otherAttributes.length) {
    const attributeToDelete = otherAttributes[index];

    // 设置删除确认弹窗信息
    deleteItemInfo.value = {
      type: '属性',
      content: `${attributeToDelete.key}: ${attributeToDelete.value}`,
      category: 'other_attribute',
      index,
    };
    showDeleteDialog.value = true;
  }
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  deleteItemInfo.value = {
    type: '',
    content: '',
    category: 'travel',
    index: -1,
  };
};

// 确认删除Item
const confirmDeleteItem = async () => {
  if (!props.personDetail || deleteItemInfo.value.index === -1) return;

  try {
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    const { category, index } = deleteItemInfo.value;

    if (category === 'travel') {
      // 删除旅行记录
      const travelHistory = attributes['旅游历史'];
      if (travelHistory) {
        const travelList = travelHistory
          .split(',')
          .map((item) => item.trim())
          .filter((item) => item);
        travelList.splice(index, 1);

        if (travelList.length > 0) {
          attributes['旅游历史'] = travelList.join(', ');
        } else {
          delete attributes['旅游历史'];
        }
      }
    } else if (category === 'food') {
      // 删除饮食偏好
      const foodPreferences = attributes['餐饮偏好'];
      if (foodPreferences) {
        const foodList = foodPreferences
          .split(',')
          .map((item) => item.trim())
          .filter((item) => item);
        foodList.splice(index, 1);

        if (foodList.length > 0) {
          attributes['餐饮偏好'] = foodList.join(', ');
        } else {
          delete attributes['餐饮偏好'];
        }
      }
    } else if (category === 'expectation') {
      // 删除期望
      const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
      const foundKey = expectationKeys.find((key) => attributes[key]);
      const targetKey = foundKey || '';
      const expectationValue = foundKey ? attributes[foundKey] : '';

      if (targetKey && expectationValue) {
        const expectationList = expectationValue
          .split(',')
          .map((item) => item.trim())
          .filter((item) => item);
        expectationList.splice(index, 1);

        if (expectationList.length > 0) {
          attributes[targetKey] = expectationList.join(', ');
        } else {
          delete attributes[targetKey];
        }
      }
    } else if (category === 'other_attribute') {
      // 删除其他属性
      const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

      // 获取其他属性列表
      const otherAttributes: Array<{ key: string; value: string }> = [];
      Object.entries(attributes).forEach(([key, value]) => {
        if (!knownKeys.includes(key) && value && value.trim()) {
          otherAttributes.push({ key, value });
        }
      });

      if (index >= 0 && index < otherAttributes.length) {
        const keyToDelete = otherAttributes[index].key;
        delete attributes[keyToDelete];
      }
    }

    // 调用API更新人员属性
    await updatePersonAttributes(attributes);
    showSuccessToast('删除成功');
  } catch (error) {
    console.error('删除失败:', error);
    showFailToast('删除失败');
  } finally {
    // 关闭弹窗
    closeDeleteDialog();
  }
};

// 更新人员属性的通用函数
const updatePersonAttributes = async (newAttributes: Record<string, string>) => {
  if (!props.personDetail) return;

  const aliases = props.personDetail.aliases || '';
  const submitAliases = aliases === '' ? '' : aliases;

  const response = await updatePerson(props.personDetail.person_id, {
    user_id: props.userId,
    canonical_name: props.personDetail.canonical_name,
    aliases: submitAliases,
    relationships: props.personDetail.relationships as string[],
    profile_summary: props.personDetail.profile_summary,
    key_attributes: newAttributes,
    is_user: props.personDetail.is_user,
    avatar: props.personDetail.avatar,
  });

  if (response && response.result === 'success') {
    // 通知父组件属性已更新
    emit('attributesUpdated', newAttributes);
  } else {
    throw new Error('更新失败');
  }
};
</script>

<style lang="scss" scoped>
.lifestyle-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.travel-section,
.food-section,
.expectation-section,
.other-attributes-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .section-edit-btn,
  .section-mic-btn,
  .section-add-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .section-edit-icon,
    .section-mic-icon {
      width: 18px;
      height: 18px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
    }

    .add-icon {
      font-size: 20px;
      font-weight: bold;
      color: #00bcd4;
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 编辑模式样式
.edit-list-container {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .edit-item-container {
    .attribute-value {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;

    .save-btn,
    .cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.save-btn {
        background: rgba(0, 188, 212, 0.2);
        border-color: #00bcd4;
        color: #00bcd4;

        &:hover {
          background: rgba(0, 188, 212, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }
      }

      &.cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 显示模式样式
.travel-content,
.food-content,
.expectation-content,
.other-attributes-content {
  .travel-info,
  .food-info,
  .expectation-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

// 其他属性内容间距统一
.other-attributes-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 所有item样式
.travel-content,
.food-content,
.expectation-content,
.other-attributes-content {
  .travel-item,
  .food-item,
  .expectation-item,
  .attribute-item {
    background: rgba(0, 188, 212, 0.05);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 16px;
    position: relative;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 188, 212, 0.5);
      background: rgba(0, 188, 212, 0.08);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
    }

    .item-display-mode {
      cursor: pointer;
      font-size: 32px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      padding-right: 40px;
      display: flex;
      align-items: center;
      gap: 8px;

      .attribute-label {
        color: #00bcd4;
        font-weight: 700;
      }

      .attribute-value {
        flex: 1;
      }
    }

    .delete-item-btn {
      position: absolute;
      top: 4px;
      right: 4px;
      background: none;
      border: none;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.6);
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;
      font-size: 18px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #ff6b6b;
        transform: scale(1.1);
      }

      .delete-icon {
        font-size: 30px;
      }
    }
  }
}

// 空状态样式
.empty-travel,
.empty-food,
.empty-expectation,
.empty-other-attributes {
  color: rgba(255, 255, 255, 0.6);
  font-size: 30px;
  font-style: italic;
  text-align: center;
  padding: 20px 0;
  background: rgba(0, 188, 212, 0.05);
  border: 1px dashed rgba(0, 188, 212, 0.3);
  border-radius: 12px;
  line-height: 1.6;
}

// 单个item编辑模式样式
.item-edit-mode {
  .item-edit-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 14px 16px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    margin-bottom: 12px;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }
  }

  .item-edit-actions {
    display: flex;
    gap: 12px;

    .item-save-btn,
    .item-cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.item-save-btn {
        background: rgba(0, 188, 212, 0.2);
        border-color: #00bcd4;
        color: #00bcd4;

        &:hover {
          background: rgba(0, 188, 212, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }
      }

      &.item-cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 其他属性特殊样式
.add-attribute-mode {
  background: rgba(0, 188, 212, 0.05);
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 16px;
  margin-top: 12px;

  .attribute-edit-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .attribute-key-input {
      flex: 1;
      min-width: 120px;
    }

    .attribute-separator {
      color: #00bcd4;
      font-size: 32px;
      font-weight: bold;
    }

    .attribute-value-input {
      flex: 2;
      min-width: 200px;
    }

    .item-edit-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }

  .item-edit-actions {
    display: flex;
    gap: 12px;

    .item-save-btn,
    .item-cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.item-save-btn {
        background: rgba(0, 188, 212, 0.2);
        border-color: #00bcd4;
        color: #00bcd4;

        &:hover {
          background: rgba(0, 188, 212, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }
      }

      &.item-cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 其他属性item编辑模式特殊样式
.attribute-item .item-edit-mode .attribute-edit-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;

  .attribute-key-input {
    flex: 1;
    min-width: 120px;
  }

  .attribute-separator {
    color: #00bcd4;
    font-size: 32px;
    font-weight: bold;
  }

  .attribute-value-input {
    flex: 2;
    min-width: 200px;
  }
}
</style>
