<template>
  <div class="weather-section" :class="{ expanded: isWeatherExpanded }">
    <div class="section-header">
      <span class="section-icon">☀️</span>
      <span class="section-title">天气分析</span>
      <div class="section-actions">
        <button class="section-mic-btn" title="语音" @click="handleWeatherMicClick">
          <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
        </button>
        <button class="section-edit-btn" title="编辑" @click="handleWeatherSectionArrowClick">
          <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
        </button>
        <button class="section-expand-btn" :title="isWeatherExpanded ? '收起' : '展开'" @click="toggleWeatherExpanded">
          <div class="triangle-icon" :class="{ expanded: isWeatherExpanded }"></div>
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isWeatherExpanded }" @click="handleWeatherContentClick">
      <!-- 编辑模式 -->
      <div v-if="showWeatherEdit" class="edit-attribute-container">
        <div class="add-attribute-container">
          <input
            v-model="weatherEditValue"
            type="text"
            class="attribute-value"
            placeholder="请输入当前城市"
            @blur="handleWeatherEditComplete"
            @keydown.enter="handleWeatherEditComplete"
            @keydown.esc="handleWeatherEditCancel"
          />
        </div>
      </div>
      <!-- 显示模式 -->
      <div v-else>
        <div v-if="loadingWeather" class="loading-text">加载中...</div>
        <div v-else-if="weatherData && weatherData.result === 'success'" class="weather-content">
          <!-- 显示当前城市的个性化提醒 -->
          <div v-if="currentCityWeatherData" class="weather-reminder" :class="{ collapsed: !isWeatherExpanded }">
            {{ currentCityWeatherData.personalized_reminder }}
          </div>
          <div v-else class="weather-suggestion" :class="{ collapsed: !isWeatherExpanded }">
            还没有关于ta的地点信息哦～快去和老董聊聊吧！
          </div>
        </div>
        <div v-else-if="weatherData && weatherData.result === 'error'" class="weather-content">
          <div class="weather-suggestion" :class="{ collapsed: !isWeatherExpanded }">
            还没有关于ta的地点信息哦～快去和老董聊聊吧！
          </div>
        </div>
        <div v-else-if="weatherData === null" class="weather-content">
          <div class="weather-error" :class="{ collapsed: !isWeatherExpanded }">
            <div class="error-title">⚠️ 天气信息获取失败</div>
            <div class="error-message">网络请求超时或服务异常</div>
            <div class="error-suggestion">💡 建议：请检查网络连接或稍后重试</div>
          </div>
        </div>
        <div v-else class="weather-content">
          <div class="weather-suggestion" :class="{ collapsed: !isWeatherExpanded }">
            还没有关于ta的地点信息哦～快去和老董聊聊吧！
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import type { IPersonDetail, IGetPersonWeatherResponse } from '@/apis/memory';
import { getPersonWeather } from '@/apis/memory';
import { updatePerson } from '@/apis/relation';
import { showFailToast, showSuccessToast } from 'vant';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  weatherMicClick: [];
  personUpdated: [personDetail: IPersonDetail];
}>();

// 响应式数据
const isWeatherExpanded = ref(false);
const showWeatherEdit = ref(false);
const weatherEditValue = ref('');
const loadingWeather = ref(false);
const weatherData = ref<IGetPersonWeatherResponse | null>(null);

// 计算属性：处理后的关键属性
const processedKeyAttributes = computed(() => {
  if (!props.personDetail?.key_attributes) {
    return {};
  }

  const attributes = props.personDetail.key_attributes;
  const processed: Record<string, string> = {};

  Object.entries(attributes).forEach(([key, value]) => {
    if (typeof value === 'string') {
      processed[key] = value;
    } else if (typeof value === 'object' && value !== null) {
      processed[key] = JSON.stringify(value);
    } else {
      processed[key] = String(value);
    }
  });

  return processed;
});

// 计算属性：获取当前城市的天气数据
const currentCityWeatherData = computed(() => {
  if (!weatherData.value || weatherData.value.result !== 'success' || !weatherData.value.weather_data) {
    return null;
  }

  // 查找当前城市的天气数据
  const currentCity = processedKeyAttributes.value['当前城市'];

  if (!currentCity) {
    return null;
  }

  // 在weather_data中查找匹配的城市数据
  const weatherEntries = Object.entries(weatherData.value.weather_data);
  const matchingEntry = weatherEntries.find(
    ([locationKey, cityData]) => locationKey === '当前城市' || cityData.city === currentCity,
  );

  return matchingEntry ? matchingEntry[1] : null;
});

// 切换天气分析展开/收起状态
const toggleWeatherExpanded = () => {
  isWeatherExpanded.value = !isWeatherExpanded.value;
};

// 处理天气分析内容点击事件 - 区分点击展开和复制操作
const handleWeatherContentClick = () => {
  // 如果点击的是编辑模式的input，不触发展开/收起
  if (showWeatherEdit.value) {
    return;
  }

  // 检查是否是文本选择操作（复制操作）
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    // 有文本被选中，这是复制操作，不触发展开/收起
    return;
  }

  // 否则触发展开/收起
  toggleWeatherExpanded();
};

// 处理天气分析mic按钮点击
const handleWeatherMicClick = () => {
  emit('weatherMicClick');
};

// 处理天气分析section-arrow点击事件
const handleWeatherSectionArrowClick = () => {
  showWeatherEdit.value = true;
  weatherEditValue.value = processedKeyAttributes.value['当前城市'] || '';

  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const input = document.querySelector('.weather-section .attribute-value') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
};

// 处理天气分析编辑完成
const handleWeatherEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    const aliases = props.personDetail.aliases || '';
    const submitAliases = aliases === '' ? '' : aliases;

    // 更新key_attributes中的当前城市
    const updatedKeyAttributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (weatherEditValue.value.trim()) {
      updatedKeyAttributes['当前城市'] = weatherEditValue.value.trim();
    } else {
      delete updatedKeyAttributes['当前城市'];
    }

    const response = await updatePerson(props.personDetail.person_id, {
      user_id: props.userId,
      canonical_name: props.personDetail.canonical_name,
      aliases: submitAliases,
      relationships: props.personDetail.relationships as string[],
      profile_summary: props.personDetail.profile_summary,
      key_attributes: updatedKeyAttributes,
      is_user: props.personDetail.is_user,
      avatar: props.personDetail.avatar,
    });

    if (response && response.result === 'success') {
      // 更新本地数据
      const updatedPersonDetail = {
        ...props.personDetail,
        key_attributes: updatedKeyAttributes,
      };
      emit('personUpdated', updatedPersonDetail);
      showSuccessToast('当前城市更新成功');

      // 重新加载天气数据
      void loadWeatherData();
    } else {
      showFailToast('当前城市更新失败');
    }
  } catch (error) {
    console.error('更新当前城市失败:', error);
    showFailToast('当前城市更新失败');
  } finally {
    showWeatherEdit.value = false;
  }
};

// 处理天气分析编辑取消
const handleWeatherEditCancel = () => {
  showWeatherEdit.value = false;
  weatherEditValue.value = '';
};

// 加载天气数据
const loadWeatherData = async () => {
  if (!props.personDetail || !processedKeyAttributes.value['当前城市']) {
    weatherData.value = null;
    return;
  }

  try {
    loadingWeather.value = true;
    console.log('🔄 [WeatherSection] 开始获取天气数据...');

    const response = await getPersonWeather({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [WeatherSection] 天气数据响应:', response);
    weatherData.value = response;
  } catch (error) {
    console.error('❌ [WeatherSection] 获取天气数据失败:', error);
    weatherData.value = null;
  } finally {
    loadingWeather.value = false;
  }
};

// 监听personDetail变化，重新加载天气数据
watch(
  () => props.personDetail,
  (newPersonDetail) => {
    if (newPersonDetail) {
      void loadWeatherData();
    }
  },
  { immediate: true },
);

// 组件挂载时加载天气数据
onMounted(() => {
  void loadWeatherData();
});
</script>

<style lang="scss" scoped>
.weather-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// 天气分析展开/收起控制
.weather-section .section-content {
  cursor: pointer;
  transition: max-height 0.3s ease;

  &:not(.expanded) {
    max-height: 200px;
    overflow: hidden;
  }

  &.expanded {
    max-height: none;
    overflow: visible;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .section-edit-btn,
  .section-mic-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .section-edit-icon,
    .section-mic-icon {
      width: 18px;
      height: 18px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
    }
  }
}

.section-expand-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid #00bcd4;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }

  .triangle-icon {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #00bcd4;
    transition: transform 0.3s ease;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

// 天气分析内容样式
.weather-content {
  .weather-reminder,
  .weather-suggestion,
  .weather-error {
    font-size: 32px;
    line-height: 1.6;
    transition: all 0.3s ease;

    &.collapsed {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .weather-error {
    .error-title,
    .error-message,
    .error-suggestion {
      margin-bottom: 8px;
    }
  }

  .weather-suggestion {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0 0 16px 0;
    font-size: 32px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
  }

  .weather-reminder {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0 0 16px 0;
    font-size: 32px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
  }
}

.edit-attribute-container {
  .add-attribute-container {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: center;
    width: 100%;
    box-sizing: border-box;

    .attribute-value {
      flex: 1;
      min-width: 0;
      max-width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }
}
</style>
